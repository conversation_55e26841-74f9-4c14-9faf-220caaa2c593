% Generated by IEEEtran.bst, version: 1.14 (2015/08/26)
\begin{thebibliography}{10}
\providecommand{\url}[1]{#1}
\csname url@samestyle\endcsname
\providecommand{\newblock}{\relax}
\providecommand{\bibinfo}[2]{#2}
\providecommand{\BIBentrySTDinterwordspacing}{\spaceskip=0pt\relax}
\providecommand{\BIBentryALTinterwordstretchfactor}{4}
\providecommand{\BIBentryALTinterwordspacing}{\spaceskip=\fontdimen2\font plus
\BIBentryALTinterwordstretchfactor\fontdimen3\font minus \fontdimen4\font\relax}
\providecommand{\BIBforeignlanguage}[2]{{%
\expandafter\ifx\csname l@#1\endcsname\relax
\typeout{** WARNING: IEEEtran.bst: No hyphenation pattern has been}%
\typeout{** loaded for the language `#1'. Using the pattern for}%
\typeout{** the default language instead.}%
\else
\language=\csname l@#1\endcsname
\fi
#2}}
\providecommand{\BIBdecl}{\relax}
\BIBdecl

\bibitem{dl}
\BIBentryALTinterwordspacing
Y.~LeCun, Y.~Bengio, and G.~Hinton, ``Deep learning,'' \emph{Nature}, vol. 521, no. 7553, p. 436–444, 2015. [Online]. Available: \url{https://doi.org/10.1038/nature14539}
\BIBentrySTDinterwordspacing

\bibitem{general_review}
\BIBentryALTinterwordspacing
S.~M. Mousavi and G.~C. Beroza, ``Machine learning in earthquake seismology,'' \emph{Annual Review of Earth and Planetary Sciences}, vol.~51, no. Volume 51, 2023, pp. 105--129, 2023. [Online]. Available: \url{https://www.annualreviews.org/content/journals/10.1146/annurev-earth-071822-100323}
\BIBentrySTDinterwordspacing

\bibitem{convnetquake}
\BIBentryALTinterwordspacing
T.~Perol, M.~Gharbi, and M.~Denolle, ``Convolutional neural network for earthquake detection and location,'' \emph{Science Advances}, vol.~4, no.~2, p. e1700578, 2018. [Online]. Available: \url{https://www.science.org/doi/abs/10.1126/sciadv.1700578}
\BIBentrySTDinterwordspacing

\bibitem{cred}
S.~M. Mousavi, W.~Zhu, Y.~Sheng, and G.~C. Beroza, ``Cred: A deep residual network of convolutional and recurrent units for earthquake signal detection,'' \emph{Scientific Reports}, vol.~9, no.~1, Jul 2019.

\bibitem{gpd}
\BIBentryALTinterwordspacing
Z.~E. Ross, M.~Meier, E.~Hauksson, and T.~H. Heaton, ``Generalized seismic phase detection with deep learning,'' \emph{Bulletin of the Seismological Society of America}, vol. 108, no.~5A, pp. 2894--2901, 08 2018. [Online]. Available: \url{https://doi.org/10.1785/0120180080}
\BIBentrySTDinterwordspacing

\bibitem{phasenet}
W.~Zhu and G.~C. Beroza, ``Phasenet: A deep-neural-network-based seismic arrival time picking method,'' \emph{Geophysical Journal International}, vol. 216, no.~1, Oct 2018.

\bibitem{eqt}
S.~M. Mousavi, W.~L. Ellsworth, W.~Zhu, L.~Y. Chuang, and G.~C. Beroza, ``Earthquake transformer—an attentive deep-learning model for simultaneous earthquake detection and phase picking,'' \emph{Nature Communications}, vol.~11, no.~1, Aug 2020.

\bibitem{magnet}
S.~M. Mousavi and G.~C. Beroza, ``A machine‐learning approach for earthquake magnitude estimation,'' \emph{Geophysical Research Letters}, vol.~47, no.~1, Jan 2020.

\bibitem{conv_parameterization}
A.~Lomax, A.~Michelini, and D.~Jozinović, ``An investigation of rapid earthquake characterization using single‐station waveforms and a convolutional neural network,'' \emph{Seismological Research Letters}, vol.~90, no.~2A, p. 517–529, Feb 2019.

\bibitem{baznet}
M.~S.~Mostafa and G.~C. Beroza, ``Bayesian-deep-learning estimation of earthquake location from single-station observations,'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, vol.~58, no.~11, p. 8211–8224, Nov 2020.

\bibitem{p_and_polarity}
\BIBentryALTinterwordspacing
Z.~E. Ross, M.-A. Meier, and E.~Hauksson, ``P wave arrival picking and first-motion polarity determination with deep learning,'' \emph{Journal of Geophysical Research: Solid Earth}, vol. 123, no.~6, pp. 5120--5129, 2018. [Online]. Available: \url{https://agupubs.onlinelibrary.wiley.com/doi/abs/10.1029/2017JB015251}
\BIBentrySTDinterwordspacing

\bibitem{japan_polarity}
S.~Hara, Y.~Fukahata, and Y.~Iio, ``P-wave first-motion polarity determination of waveform data in western japan using deep learning,'' \emph{Earth, Planets and Space}, vol.~71, no.~1, Nov 2019.

\bibitem{transformer}
\BIBentryALTinterwordspacing
A.~Vaswani, N.~Shazeer, N.~Parmar, J.~Uszkoreit, L.~Jones, A.~N. Gomez, {\L}.~Kaiser, and I.~Polosukhin, ``Attention is all you need,'' in \emph{Proceedings of the Conference and Workshop on Neural Information Processing Systems (NeurIPS)}, vol.~30, 2017. [Online]. Available: \url{https://proceedings.neurips.cc/paper_files/paper/2017/file/3f5ee243547dee91fbd053c1c4a845aa-Paper.pdf}
\BIBentrySTDinterwordspacing

\bibitem{bert}
\BIBentryALTinterwordspacing
J.~Devlin, M.-W. Chang, K.~Lee, and K.~Toutanova, ``{BERT}: Pre-training of deep bidirectional transformers for language understanding,'' in \emph{Proceedings of the 2019 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies}, Jun. 2019, pp. 4171--4186. [Online]. Available: \url{https://aclanthology.org/N19-1423}
\BIBentrySTDinterwordspacing

\bibitem{gpt2}
\BIBentryALTinterwordspacing
A.~Radford, J.~Wu, R.~Child, D.~Luan, D.~Amodei, and I.~Sutskever, ``Language models are unsupervised multitask learners,'' \emph{arXiv}, 2019. [Online]. Available: \url{https://cdn.openai.com/better-language-models/language_models_are_unsupervised_multitask_learners.pdf}
\BIBentrySTDinterwordspacing

\bibitem{clip}
A.~Radford, J.~W. Kim, C.~Hallacy, A.~Ramesh, G.~Goh, S.~Agarwal, G.~Sastry, A.~Askell, P.~Mishkin, J.~Clark, G.~Krueger, and I.~Sutskever, ``Learning transferable visual models from natural language supervision,'' \emph{arXiv preprint arXiv:2103.00020}, 2021.

\bibitem{sam}
\BIBentryALTinterwordspacing
A.~Kirillov, E.~Mintun, N.~Ravi, H.~Mao, C.~Rolland, L.~Gustafson, T.~Xiao, S.~Whitehead, A.~C. Berg, W.-Y. Lo, P.~Dollár, and R.~Girshick, ``Segment anything,'' in \emph{IEEE/CVF International Conference on Computer Vision (ICCV)}, 2023, pp. 3992--4003. [Online]. Available: \url{https://openaccess.thecvf.com/content/ICCV2023/papers/Kirillov_Segment_Anything_ICCV_2023_paper.pdf}
\BIBentrySTDinterwordspacing

\bibitem{dall-e}
\BIBentryALTinterwordspacing
A.~Ramesh, M.~Pavlov, G.~Goh, S.~Gray, C.~Voss, A.~Radford, M.~Chen, and I.~Sutskever, ``Zero-shot text-to-image generation,'' \emph{arXiv}, 2021. [Online]. Available: \url{https://arxiv.org/abs/2102.12092}
\BIBentrySTDinterwordspacing

\bibitem{alphafold}
\BIBentryALTinterwordspacing
J.~Jumper, R.~Evans, A.~Pritzel, T.~Green, M.~Figurnov, O.~Ronneberger, K.~Tunyasuvunakool, R.~Bates, A.~Žídek, A.~Potapenko, A.~Bridgland, C.~Meyer, S.~A.~A. Kohl, A.~J. Ballard, A.~Cowie, B.~Romera-Paredes, S.~Nikolov, R.~Jain, J.~Adler, and T.~Back, ``Highly accurate protein structure prediction with alphafold,'' \emph{Nature}, vol. 596, no. 7873, p. 583–589, Jul 2021. [Online]. Available: \url{https://www.nature.com/articles/s41586-021-03819-2}
\BIBentrySTDinterwordspacing

\bibitem{ofa}
Z.~Tian, N.~Peisong, W.~Xue, S.~Liang, and J.~Rong, ``{One Fits All}: Power general time series analysis by pretrained lm,'' in \emph{NeurIPS}, 2023.

\bibitem{calf}
P.~Liu, H.~Guo, T.~Dai, N.~Li, J.~Bao, X.~Ren, Y.~Jiang, and S.-T. Xia, ``Calf: Aligning llms for time series forecasting via cross-modal fine-tuning,'' in \emph{Proceedings of the AAAI Conference on Artificial Intelligence}, 2025.

\bibitem{seislm}
\BIBentryALTinterwordspacing
T.~Liu, J.~M{\"u}nchmeyer, L.~Laurenti, C.~Marone, M.~V. de~Hoop, and I.~Dokmani{\'c}, ``Seis{LM}: a foundation model for seismic waveforms,'' in \emph{Neurips 2024 Workshop Foundation Models for Science: Progress, Opportunities, and Challenges}, 2024. [Online]. Available: \url{https://openreview.net/forum?id=UQD2BM8UE4}
\BIBentrySTDinterwordspacing

\bibitem{seisclip}
X.~Si, X.~Wu, H.~Sheng, J.~Zhu, and Z.~Li, ``Seisclip: A seismology foundation model pre-trained by multimodal data for multipurpose seismic feature extraction,'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, vol.~62, p. 1–13, Jan 2024.

\bibitem{voice2series}
C.-H.~H. Yang, Y.-Y. Tsai, and P.-Y. Chen, ``Voice2series: Reprogramming acoustic models for time series classification,'' in \emph{Proceedings of the International Conference on Machine Learning (ICML)}, 2021.

\bibitem{visionts}
\BIBentryALTinterwordspacing
M.~Chen, L.~Shen, Z.~Li, X.~J. Wang, J.~Sun, and C.~Liu, ``Visionts: Visual masked autoencoders are free-lunch zero-shot time series forecasters,'' \emph{arXiv}, 2024. [Online]. Available: \url{https://arxiv.org/abs/2408.17253}
\BIBentrySTDinterwordspacing

\bibitem{llm2code}
\BIBentryALTinterwordspacing
D.~Goel, R.~Grover, and F.~H. Fard, ``On the cross-modal transfer from natural language to code through adapter modules,'' in \emph{Proceedings of the IEEE/ACM International Conference on Program Comprehension}, 2022, p. 71–81. [Online]. Available: \url{https://doi.org/10.1145/3524610.3527892}
\BIBentrySTDinterwordspacing

\bibitem{llm2protein}
\BIBentryALTinterwordspacing
I.~Melnyk, V.~Chenthamarakshan, P.-Y. Chen, P.~Das, A.~Dhurandhar, I.~Padhi, and D.~Das, ``Reprogramming pretrained language models for antibody sequence infilling,'' in \emph{Proceedings of the International Conference on Machine Learning (ICML)}, ser. Proceedings of Machine Learning Research, vol. 202.\hskip 1em plus 0.5em minus 0.4em\relax PMLR, 23--29 Jul 2023, pp. 24\,398--24\,419. [Online]. Available: \url{https://proceedings.mlr.press/v202/melnyk23a.html}
\BIBentrySTDinterwordspacing

\bibitem{dino2geo}
\BIBentryALTinterwordspacing
Z.~Guo, X.~Wu, L.~Liang, H.~Sheng, N.~Chen, and Z.~Bi, ``Cross-domain foundation model adaptation: Pioneering computer vision models for geophysical data analysis,'' \emph{arXiv}, 2024. [Online]. Available: \url{https://arxiv.org/abs/2408.12396}
\BIBentrySTDinterwordspacing

\bibitem{cross-modal_finetune}
\BIBentryALTinterwordspacing
J.~Shen, L.~Li, L.~M. Dery, C.~Staten, M.~Khodak, G.~Neubig, and A.~Talwalkar, ``Cross-modal fine-tuning: Align then refine,'' in \emph{Proceedings of the International Conference on Machine Learning (ICML)}, ser. Proceedings of Machine Learning Research, vol. 202.\hskip 1em plus 0.5em minus 0.4em\relax PMLR, 23--29 Jul 2023, pp. 31\,030--31\,056. [Online]. Available: \url{https://proceedings.mlr.press/v202/shen23e.html}
\BIBentrySTDinterwordspacing

\bibitem{multimodal}
Y.~Zhang, X.~Ding, K.~Gong, Y.~Ge, Y.~Shan, and X.~Yue, ``Multimodal pathway: Improve transformers with irrelevant data from other modalities,'' \emph{2024 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)}, vol.~35, p. 6108–6117, Jun 2024.

\bibitem{llama2}
\BIBentryALTinterwordspacing
H.~Touvron, L.~Martin, K.~Stone, P.~Albert, A.~Almahairi, Y.~Babaei, N.~Bashlykov, S.~Batra, P.~Bhargava, S.~Bhosale, D.~Bikel, L.~Blecher, C.~C. Ferrer, M.~Chen, G.~Cucurull, D.~Esiobu, J.~Fernandes, J.~Fu, W.~Fu, B.~Fuller, C.~Gao, V.~Goswami, N.~Goyal, A.~Hartshorn, S.~Hosseini, R.~Hou, H.~Inan, M.~Kardas, V.~Kerkez, M.~Khabsa, I.~Kloumann, A.~Korenev, P.~S. Koura, M.-A. Lachaux, T.~Lavril, J.~Lee, D.~Liskovich, Y.~Lu, Y.~Mao, X.~Martinet, T.~Mihaylov, P.~Mishra, I.~Molybog, Y.~Nie, A.~Poulton, J.~Reizenstein, R.~Rungta, K.~Saladi, A.~Schelten, R.~Silva, E.~M. Smith, R.~Subramanian, X.~E. Tan, B.~Tang, R.~Taylor, A.~Williams, J.~X. Kuan, P.~Xu, Z.~Yan, I.~Zarov, Y.~Zhang, A.~Fan, M.~Kambadur, S.~Narang, A.~Rodriguez, R.~Stojnic, S.~Edunov, and T.~Scialom, ``Llama 2: Open foundation and fine-tuned chat models,'' \emph{arXiv}, 2023. [Online]. Available: \url{https://arxiv.org/abs/2307.09288}
\BIBentrySTDinterwordspacing

\bibitem{test}
\BIBentryALTinterwordspacing
C.~Sun, H.~Li, Y.~Li, and S.~Hong, ``{TEST}: Text prototype aligned embedding to activate {LLM}'s ability for time series,'' in \emph{The Twelfth International Conference on Learning Representations}, 2024. [Online]. Available: \url{https://openreview.net/forum?id=Tuh4nZVb0g}
\BIBentrySTDinterwordspacing

\bibitem{time-llm}
M.~Jin, S.~Wang, L.~Ma, Z.~Chu, J.~Y. Zhang, X.~Shi, P.-Y. Chen, Y.~Liang, Y.-F. Li, S.~Pan, and Q.~Wen, ``{Time-LLM}: Time series forecasting by reprogramming large language models,'' in \emph{International Conference on Learning Representations (ICLR)}, 2024.

\bibitem{tempo}
\BIBentryALTinterwordspacing
D.~Cao, F.~Jia, S.~O. Arik, T.~Pfister, Y.~Zheng, W.~Ye, and Y.~Liu, ``{TEMPO}: Prompt-based generative pre-trained transformer for time series forecasting,'' in \emph{The Twelfth International Conference on Learning Representations}, 2024. [Online]. Available: \url{https://openreview.net/forum?id=YH5w12OUuU}
\BIBentrySTDinterwordspacing

\bibitem{s2ip-llm}
Z.~Pan, Y.~Jiang, S.~Garg, A.~Schneider, Y.~Nevmyvaka, and D.~Song, ``S\textsuperscript{2}ip-llm: Semantic space informed prompt learning with llm for time series forecasting,'' in \emph{Forty-first International Conference on Machine Learning}, 2024.

\bibitem{first-step}
D.~Spathis and F.~Kawsar, ``The first step is the hardest: pitfalls of representing and tokenizing temporal data for large language models,'' \emph{Journal of the American Medical Informatics Association}, vol.~31, no.~9, p. 2151–2158, Jul 2024.

\bibitem{batchnorm}
S.~Ioffe and C.~Szegedy, ``Batch normalization: accelerating deep network training by reducing internal covariate shift,'' in \emph{Proceedings of the 32nd International Conference on Machine Learning}, vol.~37, 2015, p. 448–456.

\bibitem{gelu}
\BIBentryALTinterwordspacing
D.~Hendrycks and K.~Gimpel, ``Gaussian error linear units (gelus),'' \emph{arXiv}, 2023. [Online]. Available: \url{https://arxiv.org/abs/1606.08415}
\BIBentrySTDinterwordspacing

\bibitem{patchtst}
Y.~Nie, N.~H.~Nguyen, P.~Sinthong, and J.~Kalagnanam, ``A time series is worth 64 words: Long-term forecasting with transformers,'' in \emph{International Conference on Learning Representations}, 2023.

\bibitem{vit}
\BIBentryALTinterwordspacing
A.~Dosovitskiy, L.~Beyer, A.~Kolesnikov, D.~Weissenborn, X.~Zhai, T.~Unterthiner, M.~Dehghani, M.~Minderer, G.~Heigold, S.~Gelly, J.~Uszkoreit, and N.~Houlsby, ``An image is worth 16x16 words: Transformers for image recognition at scale,'' in \emph{International Conference on Learning Representations}, 2021. [Online]. Available: \url{https://openreview.net/forum?id=YicbFdNTTy}
\BIBentrySTDinterwordspacing

\bibitem{hdmixer}
Q.~Huang, L.~Shen, R.~Zhang, J.~Cheng, S.~Ding, Z.~Zhou, and Y.~Wang, ``Hdmixer: Hierarchical dependency with extendable patch for multivariate time series forecasting,'' \emph{Proceedings of the AAAI Conference on Artificial Intelligence}, vol.~38, no.~11, p. 12608–12616, Mar 2024.

\bibitem{lora}
\BIBentryALTinterwordspacing
E.~J. Hu, yelong shen, P.~Wallis, Z.~Allen-Zhu, Y.~Li, S.~Wang, L.~Wang, and W.~Chen, ``Lo{RA}: Low-rank adaptation of large language models,'' in \emph{International Conference on Learning Representations}, 2022. [Online]. Available: \url{https://openreview.net/forum?id=nZeVKeeFYf9}
\BIBentrySTDinterwordspacing

\bibitem{seist}
S.~Li, X.~Yang, A.~Cao, C.~Wang, Y.~Liu, Y.~Liu, and Q.~Niu, ``Seist: A foundational deep-learning model for earthquake monitoring tasks,'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, vol.~62, p. 1–15, Jan 2024.

\bibitem{stead}
S.~M. Mousavi, Y.~Sheng, W.~Zhu, and G.~C. Beroza, ``Stanford earthquake dataset (stead): A global data set of seismic signals for ai,'' \emph{IEEE Access}, vol.~7, p. 179464–179476, 2019.

\bibitem{diting}
\BIBentryALTinterwordspacing
M.~Zhao, Z.~Xiao, S.~Chen, and L.~Fang, ``Diting: A large-scale chinese seismic benchmark dataset for artificial intelligence in seismology,'' \emph{Earthquake Science}, vol.~36, no.~2, p. 84–94, 2023. [Online]. Available: \url{https://www.sciencedirect.com/science/article/pii/S1674451922000222}
\BIBentrySTDinterwordspacing

\bibitem{adam}
D.~P. Kingma and J.~Ba, ``Adam: A method for stochastic optimization,'' in \emph{International Conference on Learning Representations (ICLR)}, 12 2014.

\bibitem{cycliclr}
L.~N. Smith, ``Cyclical learning rates for training neural networks,'' in \emph{IEEE Winter Conference on Applications of Computer Vision (WACV)}, 2017, pp. 464--472.

\bibitem{ditingmotion}
M.~Zhao, Z.~Xiao, M.~Zhang, Y.~Yang, L.~Tang, and S.~Chen, ``Ditingmotion: A deep-learning first-motion-polarity classifier and its application to focal mechanism inversion,'' \emph{Frontiers in Earth Science}, vol.~11, Mar 2023.

\bibitem{huggingface}
\BIBentryALTinterwordspacing
T.~Wolf, L.~Debut, V.~Sanh, J.~Chaumond, C.~Delangue, A.~Moi, P.~Cistac, T.~Rault, R.~Louf, M.~Funtowicz, J.~Davison, S.~Shleifer, P.~von Platen, C.~Ma, Y.~Jernite, J.~Plu, C.~Xu, T.~L. Scao, S.~Gugger, M.~Drame, Q.~Lhoest, and A.~M. Rush, ``Transformers: State-of-the-art natural language processing,'' in \emph{Proceedings of the 2020 Conference on Empirical Methods in Natural Language Processing: System Demonstrations}.\hskip 1em plus 0.5em minus 0.4em\relax Online: Association for Computational Linguistics, Oct. 2020, pp. 38--45. [Online]. Available: \url{https://www.aclweb.org/anthology/2020.emnlp-demos.6}
\BIBentrySTDinterwordspacing

\bibitem{llm-effect}
\BIBentryALTinterwordspacing
M.~Tan, M.~A. Merrill, V.~Gupta, T.~Althoff, and T.~Hartvigsen, ``Are language models actually useful for time series forecasting?'' in \emph{The Thirty-eighth Annual Conference on Neural Information Processing Systems}, 2024. [Online]. Available: \url{https://openreview.net/forum?id=DV15UbHCY1}
\BIBentrySTDinterwordspacing

\bibitem{stl}
R.~B. Cleveland, W.~S. Cleveland, J.~E. McRae, I.~Terpenning \emph{et~al.}, ``Stl: A seasonal-trend decomposition procedure based on loess,'' \emph{Journal of Official Statistics}, vol.~6, no.~1, pp. 3--73, Jan 1990.

\bibitem{whisper}
\BIBentryALTinterwordspacing
A.~Radford, J.~W. Kim, T.~Xu, G.~Brockman, C.~McLeavey, and I.~Sutskever, ``Robust speech recognition via large-scale weak supervision,'' \emph{arXiv preprint}, 2022. [Online]. Available: \url{https://arxiv.org/abs/2212.04356}
\BIBentrySTDinterwordspacing

\bibitem{convnext}
Z.~Liu, H.~Mao, C.-Y. Wu, C.~Feichtenhofer, T.~Darrell, and S.~Xie, ``A convnet for the 2020s,'' in \emph{Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)}, 2022.

\bibitem{swin-transformer}
Z.~Liu, Y.~Lin, Y.~Cao, H.~Hu, Y.~Wei, Z.~Zhang, S.~Lin, and B.~Guo, ``Swin transformer: Hierarchical vision transformer using shifted windows,'' in \emph{Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV)}, 2021.

\end{thebibliography}
